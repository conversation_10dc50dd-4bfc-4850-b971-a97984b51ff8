import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom'
import StudentConsultations from './react/StudentConsultations'

// Initialize the Student Consultations page
const initializeStudentConsultations = () => {
  const container = document.getElementById('student-consultations-container')
  if (!container) {
    console.error('Student consultations container not found')
    return
  }

  const envData = window.ENV?.STUDENT_CONSULTATIONS
  if (!envData) {
    console.error('Student consultations environment data not found')
    return
  }

  ReactDOM.render(
    <StudentConsultations
      currentUserId={envData.current_user_id}
      recentRequests={envData.recent_requests || []}
    />,
    container
  )
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeStudentConsultations)
} else {
  initializeStudentConsultations()
}

export default initializeStudentConsultations
