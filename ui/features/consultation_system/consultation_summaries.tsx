import React from 'react'
import <PERSON>actDOM from 'react-dom'
import ConsultationSummariesList from './react/ConsultationSummaries'

// Initialize the Consultation Summaries List
const initializeConsultationSummaries = () => {
  const container = document.getElementById('consultation-summaries-container')
  if (!container) {
    console.error('Consultation summaries container not found')
    return
  }

  const envData = window.ENV?.CONSULTATION_SUMMARIES
  if (!envData) {
    console.error('Consultation summaries environment data not found')
    return
  }

  ReactDOM.render(
    <ConsultationSummariesList
      currentUserId={envData.current_user_id}
      initialSummaries={envData.summaries || []}
      concernTypes={envData.concern_types || []}
      filters={envData.filters || {}}
    />,
    container
  )
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeConsultationSummaries)
} else {
  initializeConsultationSummaries()
}

export default initializeConsultationSummaries
