import React from 'react'
import { View } from '@instructure/ui-view'
import { Spinner } from '@instructure/ui-spinner'
import { Text } from '@instructure/ui-text'

interface LoadingSpinnerProps {
  message?: string
  size?: 'x-small' | 'small' | 'medium' | 'large'
  variant?: 'default' | 'inverse'
  margin?: string
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  message = 'Loading...',
  size = 'medium',
  variant = 'default',
  margin = 'large'
}) => {
  return (
    <View as="div" textAlign="center" padding={margin}>
      <Spinner
        renderTitle={message}
        size={size}
        variant={variant}
        margin="0 0 small 0"
      />
      <Text size="small" color="secondary">
        {message}
      </Text>
    </View>
  )
}

export default LoadingSpinner

// Specialized loading components for different sections
export const TimeSlotLoadingSpinner: React.FC = () => (
  <LoadingSpinner message="Loading time slots..." />
)

export const ConsultationRequestLoadingSpinner: React.FC = () => (
  <LoadingSpinner message="Loading consultation requests..." />
)

export const SummariesLoadingSpinner: React.FC = () => (
  <LoadingSpinner message="Loading consultation summaries..." />
)

export const DashboardLoadingSpinner: React.FC = () => (
  <LoadingSpinner message="Loading dashboard data..." />
)

// Inline loading spinner for smaller components
export const InlineLoadingSpinner: React.FC<{ message?: string }> = ({ 
  message = 'Loading...' 
}) => (
  <View as="div" display="inline-flex" alignItems="center" gap="x-small">
    <Spinner size="x-small" renderTitle={message} />
    <Text size="small" color="secondary">
      {message}
    </Text>
  </View>
)

// Full page loading overlay
export const FullPageLoadingSpinner: React.FC<{ message?: string }> = ({ 
  message = 'Loading consultation system...' 
}) => (
  <View 
    as="div" 
    position="fixed" 
    insetInlineStart="0" 
    insetBlockStart="0" 
    width="100%" 
    height="100%" 
    background="primary"
    display="flex"
    alignItems="center"
    justifyItems="center"
    style={{ zIndex: 9999 }}
  >
    <View as="div" textAlign="center">
      <Spinner
        renderTitle={message}
        size="large"
        margin="0 0 medium 0"
      />
      <Text size="medium" color="secondary">
        {message}
      </Text>
    </View>
  </View>
)
