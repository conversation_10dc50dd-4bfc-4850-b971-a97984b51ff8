import doFetchApi from '@canvas/do-fetch-api-effect'
import type { 
  ConsultationRequest,
  ConsultationRequestFormData,
  ConsultationRequestResponse,
  FacultyDashboardData,
  StudentFormData,
  ConsultationFilters
} from '../types'

const API_BASE = '/consultation_requests'

export const fetchConsultationRequests = async (filters?: ConsultationFilters): Promise<ConsultationRequestResponse> => {
  try {
    const { json } = await doFetchApi({
      path: API_BASE,
      method: 'GET',
      params: filters
    })
    return json as ConsultationRequestResponse
  } catch (error: any) {
    throw new Error(error.message || 'Failed to fetch consultation requests')
  }
}

export const fetchConsultationRequest = async (id: string): Promise<ConsultationRequest> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${id}`,
      method: 'GET'
    })
    return json as ConsultationRequest
  } catch (error: any) {
    throw new Error(error.message || 'Failed to fetch consultation request')
  }
}

export const createConsultationRequest = async (data: ConsultationRequestFormData): Promise<ConsultationRequest> => {
  try {
    const { json } = await doFetchApi({
      path: API_BASE,
      method: 'POST',
      body: {
        consultation_request: data
      }
    })
    return json as ConsultationRequest
  } catch (error: any) {
    const errorMessage = error.response?.errors?.join(', ') || error.message || 'Failed to create consultation request'
    throw new Error(errorMessage)
  }
}

export const updateConsultationRequest = async (id: string, data: Partial<ConsultationRequestFormData>): Promise<ConsultationRequest> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${id}`,
      method: 'PATCH',
      body: {
        consultation_request: data
      }
    })
    return json as ConsultationRequest
  } catch (error: any) {
    const errorMessage = error.response?.errors?.join(', ') || error.message || 'Failed to update consultation request'
    throw new Error(errorMessage)
  }
}

export const cancelConsultationRequest = async (id: string): Promise<void> => {
  try {
    await doFetchApi({
      path: `${API_BASE}/${id}`,
      method: 'DELETE'
    })
  } catch (error: any) {
    const errorMessage = error.response?.error || error.message || 'Failed to cancel consultation request'
    throw new Error(errorMessage)
  }
}

export const approveConsultationRequest = async (id: string, comment?: string): Promise<ConsultationRequest> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${id}/approve`,
      method: 'POST',
      body: {
        comment: comment
      }
    })
    return json as ConsultationRequest
  } catch (error: any) {
    const errorMessage = error.response?.errors?.join(', ') || error.message || 'Failed to approve consultation request'
    throw new Error(errorMessage)
  }
}

export const declineConsultationRequest = async (id: string, comment: string): Promise<ConsultationRequest> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${id}/decline`,
      method: 'POST',
      body: {
        comment: comment
      }
    })
    return json as ConsultationRequest
  } catch (error: any) {
    const errorMessage = error.response?.errors?.join(', ') || error.message || 'Failed to decline consultation request'
    throw new Error(errorMessage)
  }
}

export const completeConsultationRequest = async (id: string, completionNotes?: string): Promise<ConsultationRequest> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${id}/complete`,
      method: 'POST',
      body: {
        completion_notes: completionNotes
      }
    })
    return json as ConsultationRequest
  } catch (error: any) {
    const errorMessage = error.response?.errors?.join(', ') || error.message || 'Failed to complete consultation request'
    throw new Error(errorMessage)
  }
}

export const fetchFacultyDashboard = async (): Promise<FacultyDashboardData> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/faculty_dashboard`,
      method: 'GET'
    })
    return json as FacultyDashboardData
  } catch (error: any) {
    throw new Error(error.message || 'Failed to fetch faculty dashboard data')
  }
}

export const fetchStudentFormData = async (): Promise<StudentFormData> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/student_form`,
      method: 'GET'
    })
    return json as StudentFormData
  } catch (error: any) {
    throw new Error(error.message || 'Failed to fetch student form data')
  }
}
