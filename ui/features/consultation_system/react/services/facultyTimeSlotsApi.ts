import doFetchApi from '@canvas/do-fetch-api-effect'
import type { 
  FacultyTimeSlot, 
  TimeSlotFormData, 
  TimeSlotResponse,
  AvailableDate,
  AvailableDateTime,
  ApiError 
} from '../types'

const API_BASE = '/faculty_time_slots'

export const fetchTimeSlots = async (): Promise<TimeSlotResponse> => {
  try {
    const { json } = await doFetchApi({
      path: API_BASE,
      method: 'GET'
    })
    return json as TimeSlotResponse
  } catch (error: any) {
    throw new ApiError(error.message || 'Failed to fetch time slots')
  }
}

export const createTimeSlot = async (data: TimeSlotFormData): Promise<FacultyTimeSlot> => {
  try {
    const { json } = await doFetchApi({
      path: API_BASE,
      method: 'POST',
      body: {
        faculty_time_slot: data
      }
    })
    return json as FacultyTimeSlot
  } catch (error: any) {
    const errorMessage = error.response?.errors?.join(', ') || error.message || 'Failed to create time slot'
    throw new ApiError(errorMessage)
  }
}

export const updateTimeSlot = async (id: string, data: TimeSlotFormData): Promise<FacultyTimeSlot> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${id}`,
      method: 'PATCH',
      body: {
        faculty_time_slot: data
      }
    })
    return json as FacultyTimeSlot
  } catch (error: any) {
    const errorMessage = error.response?.errors?.join(', ') || error.message || 'Failed to update time slot'
    throw new ApiError(errorMessage)
  }
}

export const deleteTimeSlot = async (id: string): Promise<void> => {
  try {
    await doFetchApi({
      path: `${API_BASE}/${id}`,
      method: 'DELETE'
    })
  } catch (error: any) {
    const errorMessage = error.response?.error || error.message || 'Failed to delete time slot'
    throw new ApiError(errorMessage)
  }
}

export const fetchAvailableDates = async (startDate: string, endDate: string): Promise<AvailableDate[]> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/available_dates`,
      method: 'GET',
      params: {
        start_date: startDate,
        end_date: endDate
      }
    })
    return json.available_dates as AvailableDate[]
  } catch (error: any) {
    throw new ApiError(error.message || 'Failed to fetch available dates')
  }
}

export const fetchAvailableTimes = async (date: string): Promise<AvailableDateTime[]> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/available_times`,
      method: 'GET',
      params: {
        date: date
      }
    })
    return json.available_times as AvailableDateTime[]
  } catch (error: any) {
    throw new ApiError(error.message || 'Failed to fetch available times')
  }
}

// Helper class for API errors
class ApiError extends Error {
  public status?: number
  public errors?: string[]

  constructor(message: string, status?: number, errors?: string[]) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.errors = errors
  }
}
