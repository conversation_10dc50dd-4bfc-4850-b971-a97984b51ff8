import React, { useState, useEffect } from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Alert } from '@instructure/ui-alerts'
import { Spinner } from '@instructure/ui-spinner'
import { IconUserLine, IconCalendarMonthLine } from '@instructure/ui-icons'
import ConsultationRequestForm from './ConsultationRequestForm'
import { createConsultationRequest } from '../services/consultationRequestsApi'
import { fetchAvailableDates, fetchAvailableTimes } from '../services/facultyTimeSlotsApi'
import type { 
  StudentInfo, 
  FacultyUser, 
  ConsultationRequestFormData,
  AvailableDate,
  AvailableDateTime
} from '../types'

interface StudentConsultationFormProps {
  currentUserId: string
  studentInfo: StudentInfo
  availableFaculty: FacultyUser[]
  concernTypes: string[]
}

const StudentConsultationForm: React.FC<StudentConsultationFormProps> = ({
  currentUserId,
  studentInfo,
  availableFaculty,
  concernTypes
}) => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [availableDates, setAvailableDates] = useState<AvailableDate[]>([])
  const [availableTimes, setAvailableTimes] = useState<AvailableDateTime[]>([])
  const [selectedFaculty, setSelectedFaculty] = useState<string>('')
  const [selectedDate, setSelectedDate] = useState<string>('')

  const handleFacultyChange = async (facultyId: string) => {
    setSelectedFaculty(facultyId)
    setSelectedDate('')
    setAvailableDates([])
    setAvailableTimes([])

    if (!facultyId) return

    try {
      setLoading(true)
      const startDate = new Date().toISOString().split('T')[0]
      const endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      
      const dates = await fetchAvailableDates(startDate, endDate)
      setAvailableDates(dates)
    } catch (err: any) {
      setError('Failed to load available dates. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleDateChange = async (date: string) => {
    setSelectedDate(date)
    setAvailableTimes([])

    if (!date) return

    try {
      setLoading(true)
      const times = await fetchAvailableTimes(date)
      setAvailableTimes(times)
    } catch (err: any) {
      setError('Failed to load available times. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (formData: ConsultationRequestFormData) => {
    try {
      setLoading(true)
      setError(null)
      
      await createConsultationRequest(formData)
      
      setSuccess('Your consultation request has been submitted successfully! You will be notified when the faculty member responds.')
      
      // Reset form
      setSelectedFaculty('')
      setSelectedDate('')
      setAvailableDates([])
      setAvailableTimes([])
      
      setTimeout(() => setSuccess(null), 10000)
    } catch (err: any) {
      setError(err.message || 'Failed to submit consultation request. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const clearMessages = () => {
    setError(null)
    setSuccess(null)
  }

  return (
    <div className="consultation-system">
      <View as="div" padding="large">
        <div className="page-header">
          <Heading level="h1" margin="0 0 small 0">
            <IconUserLine /> Request Consultation
          </Heading>
          <p>Submit a request for consultation with faculty members. Please provide detailed information about your concern to help faculty prepare for your session.</p>
        </div>

        {error && (
          <Alert variant="error" margin="0 0 medium 0" onDismiss={clearMessages}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert variant="success" margin="0 0 medium 0" onDismiss={clearMessages}>
            {success}
          </Alert>
        )}

        <View as="div" background="secondary" padding="medium" borderRadius="medium" margin="0 0 large 0">
          <Heading level="h3" margin="0 0 small 0">
            Student Information
          </Heading>
          <View as="div" display="flex" gap="large">
            <View as="div">
              <strong>Name:</strong> {studentInfo.name}
            </View>
            <View as="div">
              <strong>Student ID:</strong> {studentInfo.student_id}
            </View>
          </View>
        </View>

        {availableFaculty.length === 0 ? (
          <View as="div" textAlign="center" padding="x-large">
            <div className="empty-state">
              <div className="empty-icon">
                <IconCalendarMonthLine size="large" />
              </div>
              <Heading level="h3" margin="0 0 small 0">
                No Faculty Available
              </Heading>
              <p>There are currently no faculty members with available consultation time slots. Please check back later or contact your academic advisor.</p>
            </div>
          </View>
        ) : (
          <ConsultationRequestForm
            studentInfo={studentInfo}
            availableFaculty={availableFaculty}
            concernTypes={concernTypes}
            availableDates={availableDates}
            availableTimes={availableTimes}
            selectedFaculty={selectedFaculty}
            selectedDate={selectedDate}
            onFacultyChange={handleFacultyChange}
            onDateChange={handleDateChange}
            onSubmit={handleSubmit}
            loading={loading}
          />
        )}

        <View as="div" background="secondary" padding="medium" borderRadius="medium" margin="large 0 0 0">
          <Heading level="h4" margin="0 0 small 0">
            Important Information
          </Heading>
          <ul>
            <li>Consultation requests are typically reviewed within 24-48 hours</li>
            <li>You will receive a notification when your request is approved or declined</li>
            <li>Please arrive on time for your scheduled consultation</li>
            <li>If you need to cancel, please do so at least 2 hours in advance</li>
            <li>For urgent matters, contact your academic advisor directly</li>
          </ul>
        </View>
      </View>
    </div>
  )
}

export default StudentConsultationForm
