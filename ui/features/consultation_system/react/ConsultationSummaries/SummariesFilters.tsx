import React, { useState } from 'react'
import { View } from '@instructure/ui-view'
import { Button } from '@instructure/ui-buttons'
import { TextInput } from '@instructure/ui-text-input'
import { Select } from '@instructure/ui-select'
import { Grid } from '@instructure/ui-grid'
import { IconSearchLine, IconXLine } from '@instructure/ui-icons'
import type { ConsultationFilters } from '../types'

interface SummariesFiltersProps {
  filters: ConsultationFilters
  concernTypes: string[]
  onFiltersChange: (filters: ConsultationFilters) => void
  loading: boolean
}

const SummariesFilters: React.FC<SummariesFiltersProps> = ({
  filters,
  concernTypes,
  onFiltersChange,
  loading
}) => {
  const [localFilters, setLocalFilters] = useState<ConsultationFilters>(filters)

  const handleFilterChange = (key: keyof ConsultationFilters, value: string) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleApplyFilters = () => {
    onFiltersChange(localFilters)
  }

  const handleClearFilters = () => {
    const clearedFilters = {}
    setLocalFilters(clearedFilters)
    onFiltersChange(clearedFilters)
  }

  const hasActiveFilters = Object.values(localFilters).some(value => value && value !== '')

  return (
    <View as="div" background="secondary" padding="medium" borderRadius="medium" margin="0 0 medium 0">
      <Grid>
        <Grid.Row>
          <Grid.Col width={3}>
            <Select
              renderLabel="Concern Type"
              placeholder="All types"
              value={localFilters.concern_type || ''}
              onChange={(e, { value }) => handleFilterChange('concern_type', value as string)}
            >
              <Select.Option id="all-types" value="">
                All Types
              </Select.Option>
              {concernTypes.map(type => (
                <Select.Option key={type} id={type} value={type}>
                  {type}
                </Select.Option>
              ))}
            </Select>
          </Grid.Col>
          
          <Grid.Col width={3}>
            <TextInput
              renderLabel="Search Students"
              placeholder="Student name or ID"
              value={localFilters.student_search || ''}
              onChange={(e) => handleFilterChange('student_search', e.target.value)}
            />
          </Grid.Col>
          
          <Grid.Col width={3}>
            <TextInput
              renderLabel="Search Content"
              placeholder="Notes, outcomes, etc."
              value={localFilters.content_search || ''}
              onChange={(e) => handleFilterChange('content_search', e.target.value)}
            />
          </Grid.Col>
          
          <Grid.Col width={3}>
            <View as="div" display="flex" gap="small" alignItems="end" height="100%">
              <Button
                color="primary"
                renderIcon={IconSearchLine}
                onClick={handleApplyFilters}
                disabled={loading}
              >
                Search
              </Button>
              {hasActiveFilters && (
                <Button
                  renderIcon={IconXLine}
                  onClick={handleClearFilters}
                  disabled={loading}
                >
                  Clear
                </Button>
              )}
            </View>
          </Grid.Col>
        </Grid.Row>
        
        <Grid.Row>
          <Grid.Col width={3}>
            <TextInput
              renderLabel="Start Date"
              type="date"
              value={localFilters.start_date || ''}
              onChange={(e) => handleFilterChange('start_date', e.target.value)}
            />
          </Grid.Col>
          
          <Grid.Col width={3}>
            <TextInput
              renderLabel="End Date"
              type="date"
              value={localFilters.end_date || ''}
              onChange={(e) => handleFilterChange('end_date', e.target.value)}
            />
          </Grid.Col>
          
          <Grid.Col width={6}>
            <View as="div" padding="small 0 0 0">
              <View as="div" display="flex" gap="medium" alignItems="center">
                <label>
                  <input
                    type="checkbox"
                    checked={localFilters.with_referrals === 'true'}
                    onChange={(e) => handleFilterChange('with_referrals', e.target.checked ? 'true' : '')}
                  />
                  <span style={{ marginLeft: '0.5rem' }}>With Referrals</span>
                </label>
                <label>
                  <input
                    type="checkbox"
                    checked={localFilters.requires_follow_up === 'true'}
                    onChange={(e) => handleFilterChange('requires_follow_up', e.target.checked ? 'true' : '')}
                  />
                  <span style={{ marginLeft: '0.5rem' }}>Requires Follow-up</span>
                </label>
              </View>
            </View>
          </Grid.Col>
        </Grid.Row>
      </Grid>
    </View>
  )
}

export default SummariesFilters
