import React from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Text } from '@instructure/ui-text'
import { Grid } from '@instructure/ui-grid'
import { ProgressBar } from '@instructure/ui-progress'
import { IconDocumentLine, IconUserLine, IconStatsLine, IconCalendarMonthLine } from '@instructure/ui-icons'
import type { ConsultationStatistics } from '../types'

interface StatisticsOverviewProps {
  statistics: ConsultationStatistics
}

const StatisticsOverview: React.FC<StatisticsOverviewProps> = ({ statistics }) => {
  const {
    total_consultations = 0,
    by_concern_type = {},
    with_referrals = 0,
    requiring_follow_up = 0,
    average_per_month = 0
  } = statistics

  const concernTypes = Object.keys(by_concern_type)
  const maxConcernCount = Math.max(...Object.values(by_concern_type), 1)

  const formatPercentage = (value: number, total: number) => {
    if (total === 0) return '0%'
    return `${Math.round((value / total) * 100)}%`
  }

  return (
    <View as="div">
      <Heading level="h2" margin="0 0 medium 0">
        Statistics Overview
      </Heading>
      
      {/* Main Statistics Cards */}
      <Grid>
        <Grid.Row>
          <Grid.Col width={3}>
            <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center" height="100%">
              <View as="div" margin="0 0 small 0">
                <IconDocumentLine size="medium" color="brand" />
              </View>
              <Text size="xx-large" weight="bold" color="brand" display="block">
                {total_consultations}
              </Text>
              <Text size="small" color="secondary">
                Total Consultations
              </Text>
            </View>
          </Grid.Col>
          
          <Grid.Col width={3}>
            <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center" height="100%">
              <View as="div" margin="0 0 small 0">
                <IconUserLine size="medium" color="success" />
              </View>
              <Text size="xx-large" weight="bold" color="success" display="block">
                {with_referrals}
              </Text>
              <Text size="small" color="secondary">
                With Referrals
              </Text>
              <Text size="x-small" color="secondary" display="block">
                {formatPercentage(with_referrals, total_consultations)}
              </Text>
            </View>
          </Grid.Col>
          
          <Grid.Col width={3}>
            <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center" height="100%">
              <View as="div" margin="0 0 small 0">
                <IconCalendarMonthLine size="medium" color="warning" />
              </View>
              <Text size="xx-large" weight="bold" color="warning" display="block">
                {requiring_follow_up}
              </Text>
              <Text size="small" color="secondary">
                Need Follow-up
              </Text>
              <Text size="x-small" color="secondary" display="block">
                {formatPercentage(requiring_follow_up, total_consultations)}
              </Text>
            </View>
          </Grid.Col>
          
          <Grid.Col width={3}>
            <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center" height="100%">
              <View as="div" margin="0 0 small 0">
                <IconStatsLine size="medium" color="brand" />
              </View>
              <Text size="xx-large" weight="bold" color="brand" display="block">
                {Math.round(average_per_month)}
              </Text>
              <Text size="small" color="secondary">
                Average per Month
              </Text>
            </View>
          </Grid.Col>
        </Grid.Row>
      </Grid>

      {/* Concern Types Breakdown */}
      {concernTypes.length > 0 && (
        <View as="div" margin="large 0 0 0">
          <Heading level="h3" margin="0 0 medium 0">
            Consultations by Concern Type
          </Heading>
          
          <View as="div" background="primary" padding="medium" borderRadius="medium">
            <Grid>
              <Grid.Row>
                {concernTypes.map(concernType => {
                  const count = by_concern_type[concernType]
                  const percentage = total_consultations > 0 ? (count / total_consultations) * 100 : 0
                  const progressPercentage = maxConcernCount > 0 ? (count / maxConcernCount) * 100 : 0
                  
                  return (
                    <Grid.Col key={concernType} width={concernTypes.length <= 3 ? 4 : 6}>
                      <View as="div" margin="0 0 medium 0">
                        <View as="div" display="flex" justifyItems="space-between" margin="0 0 x-small 0">
                          <Text size="small" weight="bold">
                            {concernType}
                          </Text>
                          <Text size="small" color="secondary">
                            {count} ({Math.round(percentage)}%)
                          </Text>
                        </View>
                        <ProgressBar
                          screenReaderLabel={`${concernType}: ${count} consultations`}
                          valueNow={progressPercentage}
                          valueMax={100}
                          size="small"
                          variant="brand"
                        />
                      </View>
                    </Grid.Col>
                  )
                })}
              </Grid.Row>
            </Grid>
          </View>
        </View>
      )}

      {/* Quick Insights */}
      {total_consultations > 0 && (
        <View as="div" margin="large 0 0 0">
          <Heading level="h3" margin="0 0 medium 0">
            Quick Insights
          </Heading>
          
          <View as="div" background="secondary" padding="medium" borderRadius="medium">
            <Grid>
              <Grid.Row>
                <Grid.Col width={6}>
                  <View as="div">
                    <Text size="small" weight="bold" display="block" margin="0 0 x-small 0">
                      Referral Rate
                    </Text>
                    <Text size="small">
                      You've made referrals in {formatPercentage(with_referrals, total_consultations)} of your consultations.
                      {with_referrals / total_consultations > 0.3 ? 
                        ' This shows good connection to support resources.' :
                        ' Consider if more students might benefit from referrals.'
                      }
                    </Text>
                  </View>
                </Grid.Col>
                
                <Grid.Col width={6}>
                  <View as="div">
                    <Text size="small" weight="bold" display="block" margin="0 0 x-small 0">
                      Follow-up Tracking
                    </Text>
                    <Text size="small">
                      {requiring_follow_up > 0 ? 
                        `You have ${requiring_follow_up} consultation${requiring_follow_up !== 1 ? 's' : ''} requiring follow-up.` :
                        'Great job staying on top of follow-ups!'
                      }
                      {requiring_follow_up / total_consultations > 0.2 && 
                        ' Consider scheduling regular check-ins.'
                      }
                    </Text>
                  </View>
                </Grid.Col>
              </Grid.Row>
            </Grid>
          </View>
        </View>
      )}

      {total_consultations === 0 && (
        <View as="div" background="secondary" padding="large" borderRadius="medium" textAlign="center">
          <Text color="secondary">
            No consultation data available yet. Statistics will appear here once you start conducting consultations.
          </Text>
        </View>
      )}
    </View>
  )
}

export default StatisticsOverview
