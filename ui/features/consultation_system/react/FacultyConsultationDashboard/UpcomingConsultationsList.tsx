import React, { useState } from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import { Badge } from '@instructure/ui-badge'
import { TextArea } from '@instructure/ui-text-area'
import { Modal } from '@instructure/ui-modal'
import { IconCheckMarkSolid, IconCalendarMonthLine, IconUserLine, IconClockLine } from '@instructure/ui-icons'
import type { ConsultationRequest } from '../types'

interface UpcomingConsultationsListProps {
  consultations: ConsultationRequest[]
  onComplete: (id: string, completionNotes?: string) => Promise<void>
  loading: boolean
}

const UpcomingConsultationsList: React.FC<UpcomingConsultationsListProps> = ({
  consultations,
  onComplete,
  loading
}) => {
  const [selectedConsultation, setSelectedConsultation] = useState<ConsultationRequest | null>(null)
  const [showCompleteModal, setShowCompleteModal] = useState(false)
  const [completionNotes, setCompletionNotes] = useState('')
  const [submitting, setSubmitting] = useState(false)

  const handleOpenCompleteModal = (consultation: ConsultationRequest) => {
    setSelectedConsultation(consultation)
    setShowCompleteModal(true)
    setCompletionNotes('')
  }

  const handleCloseCompleteModal = () => {
    setSelectedConsultation(null)
    setShowCompleteModal(false)
    setCompletionNotes('')
  }

  const handleComplete = async () => {
    if (!selectedConsultation) return

    try {
      setSubmitting(true)
      await onComplete(selectedConsultation.id, completionNotes || undefined)
      handleCloseCompleteModal()
    } catch (error) {
      // Error handling is done in parent component
    } finally {
      setSubmitting(false)
    }
  }

  const formatDateTime = (dateTimeString: string) => {
    try {
      const date = new Date(dateTimeString)
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } catch {
      return dateTimeString
    }
  }

  const getTimeUntilConsultation = (dateTimeString: string) => {
    try {
      const consultationTime = new Date(dateTimeString)
      const now = new Date()
      const diffMs = consultationTime.getTime() - now.getTime()
      
      if (diffMs < 0) {
        return 'Past due'
      }
      
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
      const diffDays = Math.floor(diffHours / 24)
      
      if (diffDays > 0) {
        return `In ${diffDays} day${diffDays !== 1 ? 's' : ''}`
      } else if (diffHours > 0) {
        return `In ${diffHours} hour${diffHours !== 1 ? 's' : ''}`
      } else {
        const diffMinutes = Math.floor(diffMs / (1000 * 60))
        return `In ${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''}`
      }
    } catch {
      return ''
    }
  }

  const isConsultationSoon = (dateTimeString: string) => {
    try {
      const consultationTime = new Date(dateTimeString)
      const now = new Date()
      const diffMs = consultationTime.getTime() - now.getTime()
      const diffHours = diffMs / (1000 * 60 * 60)
      
      return diffHours <= 24 && diffHours > 0
    } catch {
      return false
    }
  }

  const isConsultationPast = (dateTimeString: string) => {
    try {
      const consultationTime = new Date(dateTimeString)
      const now = new Date()
      return consultationTime.getTime() < now.getTime()
    } catch {
      return false
    }
  }

  if (consultations.length === 0) {
    return (
      <View as="div" textAlign="center" padding="x-large">
        <div className="empty-state">
          <div className="empty-icon">
            <IconCalendarMonthLine size="large" />
          </div>
          <Heading level="h3" margin="0 0 small 0">
            No Upcoming Consultations
          </Heading>
          <Text>
            You don't have any upcoming consultations scheduled. 
            Approved consultation requests will appear here.
          </Text>
        </div>
      </View>
    )
  }

  return (
    <>
      <View as="div" margin="medium 0 0 0">
        <Heading level="h3" margin="0 0 medium 0">
          Upcoming Consultations ({consultations.length})
        </Heading>
        
        {consultations.map(consultation => (
          <View
            key={consultation.id}
            as="div"
            background="primary"
            padding="medium"
            borderRadius="medium"
            borderWidth="small"
            borderColor={isConsultationSoon(consultation.preferred_datetime) ? 'warning' : 'brand'}
            margin="0 0 medium 0"
          >
            <View as="div" display="flex" justifyItems="space-between" alignItems="start">
              <View as="div" width="70%">
                <View as="div" display="flex" alignItems="center" margin="0 0 small 0">
                  <IconUserLine size="small" />
                  <Heading level="h4" margin="0 0 0 x-small">
                    {consultation.student_name}
                  </Heading>
                  <Text size="small" color="secondary" margin="0 0 0 small">
                    ({consultation.student_id})
                  </Text>
                </View>

                <View as="div" margin="0 0 small 0">
                  <View as="div" display="flex" alignItems="center" margin="0 0 x-small 0">
                    <IconCalendarMonthLine size="x-small" />
                    <Text weight="bold" margin="0 0 0 x-small">
                      {formatDateTime(consultation.preferred_datetime)}
                    </Text>
                  </View>
                  <View as="div" display="flex" alignItems="center" margin="x-small 0 0 0">
                    <IconClockLine size="x-small" />
                    <Text 
                      size="small" 
                      color={isConsultationSoon(consultation.preferred_datetime) ? 'warning' : 'secondary'}
                      margin="0 0 0 x-small"
                    >
                      {getTimeUntilConsultation(consultation.preferred_datetime)}
                    </Text>
                  </View>
                </View>

                <View as="div" margin="0 0 small 0">
                  <Badge
                    type="count"
                    text={consultation.concern_type_display}
                  />
                  <Badge
                    type="success"
                    text="Approved"
                    margin="0 0 0 x-small"
                  />
                  {isConsultationSoon(consultation.preferred_datetime) && (
                    <Badge
                      type="warning"
                      text="Soon"
                      margin="0 0 0 x-small"
                    />
                  )}
                  {isConsultationPast(consultation.preferred_datetime) && (
                    <Badge
                      type="danger"
                      text="Past Due"
                      margin="0 0 0 x-small"
                    />
                  )}
                </View>

                <View as="div" margin="small 0 0 0" background="secondary" padding="small" borderRadius="small">
                  <Text size="small" weight="bold" display="block" margin="0 0 x-small 0">
                    Student's Concern:
                  </Text>
                  <Text size="small">
                    {consultation.description}
                  </Text>
                </View>

                {consultation.faculty_comment && (
                  <View as="div" margin="small 0 0 0" background="success" padding="small" borderRadius="small">
                    <Text size="small" weight="bold" display="block" margin="0 0 x-small 0">
                      Your Comment:
                    </Text>
                    <Text size="small">
                      {consultation.faculty_comment}
                    </Text>
                  </View>
                )}

                <View as="div" margin="small 0 0 0">
                  <Text size="x-small" color="secondary">
                    Approved: {new Date(consultation.updated_at).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric',
                      hour: 'numeric',
                      minute: '2-digit'
                    })}
                  </Text>
                </View>
              </View>

              <View as="div" display="flex" direction="column" gap="small">
                {consultation.can_be_completed && (
                  <Button
                    color="success"
                    size="small"
                    renderIcon={IconCheckMarkSolid}
                    onClick={() => handleOpenCompleteModal(consultation)}
                    disabled={loading}
                  >
                    Mark Complete
                  </Button>
                )}
                <Button
                  size="small"
                  href={`/calendar?event_id=${consultation.id}`}
                  target="_blank"
                >
                  View in Calendar
                </Button>
              </View>
            </View>
          </View>
        ))}
      </View>

      <Modal
        open={showCompleteModal}
        onDismiss={handleCloseCompleteModal}
        size="medium"
        label="Complete Consultation"
      >
        <Modal.Header>
          <Heading level="h2">
            Complete Consultation
          </Heading>
        </Modal.Header>
        
        <Modal.Body>
          {selectedConsultation && (
            <View as="div">
              <View as="div" margin="0 0 medium 0">
                <Text weight="bold">Student:</Text> {selectedConsultation.student_name} ({selectedConsultation.student_id})
              </View>
              <View as="div" margin="0 0 medium 0">
                <Text weight="bold">Consultation Time:</Text> {formatDateTime(selectedConsultation.preferred_datetime)}
              </View>
              <View as="div" margin="0 0 medium 0">
                <Text weight="bold">Concern Type:</Text> {selectedConsultation.concern_type_display}
              </View>
              
              <TextArea
                label="Completion Notes (Optional)"
                placeholder="Add any notes about the consultation outcome, follow-up actions, or referrals made..."
                value={completionNotes}
                onChange={(e) => setCompletionNotes(e.target.value)}
                height="8rem"
              />
              
              <View as="div" margin="medium 0 0 0" background="secondary" padding="small" borderRadius="small">
                <Text size="small">
                  <strong>Note:</strong> Marking this consultation as complete will create a consultation summary 
                  that you can view and edit later in the Consultation Summaries section.
                </Text>
              </View>
            </View>
          )}
        </Modal.Body>
        
        <Modal.Footer>
          <Button onClick={handleCloseCompleteModal} disabled={submitting}>
            Cancel
          </Button>
          <Button
            color="success"
            onClick={handleComplete}
            disabled={submitting}
            margin="0 0 0 x-small"
          >
            {submitting ? 'Completing...' : 'Mark as Complete'}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  )
}

export default UpcomingConsultationsList
