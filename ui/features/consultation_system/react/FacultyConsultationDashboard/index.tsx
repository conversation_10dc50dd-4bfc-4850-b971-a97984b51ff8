import React, { useState, useEffect } from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Alert } from '@instructure/ui-alerts'
import { Spinner } from '@instructure/ui-spinner'
import { Tabs } from '@instructure/ui-tabs'
import { Badge } from '@instructure/ui-badge'
import { IconDashboardLine, IconClockLine, IconDocumentLine } from '@instructure/ui-icons'
import PendingRequestsList from './PendingRequestsList'
import UpcomingConsultationsList from './UpcomingConsultationsList'
import StatisticsOverview from './StatisticsOverview'
import { fetchFacultyDashboard, approveConsultationRequest, declineConsultationRequest, completeConsultationRequest } from '../services/consultationRequestsApi'
import type { ConsultationRequest, ConsultationStatistics } from '../types'

interface FacultyConsultationDashboardProps {
  currentUserId: string
  pendingRequests?: ConsultationRequest[]
  upcomingConsultations?: ConsultationRequest[]
  statistics?: ConsultationStatistics
}

const FacultyConsultationDashboard: React.FC<FacultyConsultationDashboardProps> = ({
  currentUserId,
  pendingRequests: initialPendingRequests = [],
  upcomingConsultations: initialUpcomingConsultations = [],
  statistics: initialStatistics = {} as ConsultationStatistics
}) => {
  const [pendingRequests, setPendingRequests] = useState<ConsultationRequest[]>(initialPendingRequests)
  const [upcomingConsultations, setUpcomingConsultations] = useState<ConsultationRequest[]>(initialUpcomingConsultations)
  const [statistics, setStatistics] = useState<ConsultationStatistics>(initialStatistics)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [selectedTab, setSelectedTab] = useState('pending')

  useEffect(() => {
    if (initialPendingRequests.length === 0 && initialUpcomingConsultations.length === 0) {
      loadDashboardData()
    }
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await fetchFacultyDashboard()
      setPendingRequests(data.pending_requests)
      setUpcomingConsultations(data.upcoming_consultations)
      setStatistics(data.statistics)
    } catch (err: any) {
      setError('Failed to load dashboard data. Please try again.')
      console.error('Error loading dashboard data:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleApproveRequest = async (id: string, comment?: string) => {
    try {
      setLoading(true)
      setError(null)
      const updatedRequest = await approveConsultationRequest(id, comment)
      
      // Move from pending to upcoming
      setPendingRequests(prev => prev.filter(req => req.id !== id))
      setUpcomingConsultations(prev => [...prev, updatedRequest])
      
      setSuccess('Consultation request approved successfully!')
      setTimeout(() => setSuccess(null), 5000)
    } catch (err: any) {
      setError(err.message || 'Failed to approve request. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleDeclineRequest = async (id: string, comment: string) => {
    try {
      setLoading(true)
      setError(null)
      await declineConsultationRequest(id, comment)
      
      // Remove from pending requests
      setPendingRequests(prev => prev.filter(req => req.id !== id))
      
      setSuccess('Consultation request declined.')
      setTimeout(() => setSuccess(null), 5000)
    } catch (err: any) {
      setError(err.message || 'Failed to decline request. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleCompleteConsultation = async (id: string, completionNotes?: string) => {
    try {
      setLoading(true)
      setError(null)
      await completeConsultationRequest(id, completionNotes)
      
      // Remove from upcoming consultations
      setUpcomingConsultations(prev => prev.filter(req => req.id !== id))
      
      setSuccess('Consultation marked as completed!')
      setTimeout(() => setSuccess(null), 5000)
    } catch (err: any) {
      setError(err.message || 'Failed to complete consultation. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const clearMessages = () => {
    setError(null)
    setSuccess(null)
  }

  return (
    <div className="consultation-system">
      <View as="div" padding="large">
        <div className="page-header">
          <Heading level="h1" margin="0 0 small 0">
            <IconDashboardLine /> Faculty Consultation Dashboard
          </Heading>
          <p>Manage your consultation requests, view upcoming appointments, and track your consultation statistics.</p>
        </div>

        {error && (
          <Alert variant="error" margin="0 0 medium 0" onDismiss={clearMessages}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert variant="success" margin="0 0 medium 0" onDismiss={clearMessages}>
            {success}
          </Alert>
        )}

        <StatisticsOverview statistics={statistics} />

        <View as="div" margin="large 0 0 0">
          <Tabs
            variant="secondary"
            onRequestTabChange={(event, { index }) => {
              const tabs = ['pending', 'upcoming', 'manage-slots']
              setSelectedTab(tabs[index])
            }}
          >
            <Tabs.Panel
              id="pending-tab"
              renderTitle={() => (
                <View as="div" display="flex" alignItems="center">
                  <span>Pending Requests</span>
                  {pendingRequests.length > 0 && (
                    <Badge
                      count={pendingRequests.length}
                      margin="0 0 0 x-small"
                      type="notification"
                    />
                  )}
                </View>
              )}
              isSelected={selectedTab === 'pending'}
            >
              {loading && selectedTab === 'pending' ? (
                <View as="div" textAlign="center" padding="large">
                  <Spinner renderTitle="Loading pending requests..." />
                </View>
              ) : (
                <PendingRequestsList
                  requests={pendingRequests}
                  onApprove={handleApproveRequest}
                  onDecline={handleDeclineRequest}
                  loading={loading}
                />
              )}
            </Tabs.Panel>

            <Tabs.Panel
              id="upcoming-tab"
              renderTitle={() => (
                <View as="div" display="flex" alignItems="center">
                  <IconClockLine size="x-small" />
                  <span style={{ marginLeft: '0.5rem' }}>Upcoming Consultations</span>
                  {upcomingConsultations.length > 0 && (
                    <Badge
                      count={upcomingConsultations.length}
                      margin="0 0 0 x-small"
                      type="count"
                    />
                  )}
                </View>
              )}
              isSelected={selectedTab === 'upcoming'}
            >
              {loading && selectedTab === 'upcoming' ? (
                <View as="div" textAlign="center" padding="large">
                  <Spinner renderTitle="Loading upcoming consultations..." />
                </View>
              ) : (
                <UpcomingConsultationsList
                  consultations={upcomingConsultations}
                  onComplete={handleCompleteConsultation}
                  loading={loading}
                />
              )}
            </Tabs.Panel>

            <Tabs.Panel
              id="manage-slots-tab"
              renderTitle={() => (
                <View as="div" display="flex" alignItems="center">
                  <IconDocumentLine size="x-small" />
                  <span style={{ marginLeft: '0.5rem' }}>Quick Actions</span>
                </View>
              )}
              isSelected={selectedTab === 'manage-slots'}
            >
              <View as="div" padding="medium">
                <Heading level="h3" margin="0 0 medium 0">
                  Quick Actions
                </Heading>
                <View as="div" display="flex" gap="medium" wrap="wrap">
                  <Button
                    color="primary"
                    href="/faculty_time_slots"
                    renderIcon={IconClockLine}
                  >
                    Manage Time Slots
                  </Button>
                  <Button
                    href="/consultation_summaries"
                    renderIcon={IconDocumentLine}
                  >
                    View All Summaries
                  </Button>
                  <Button
                    href="/consultation_summaries/reports"
                    renderIcon={IconDashboardLine}
                  >
                    View Reports
                  </Button>
                  <Button
                    onClick={loadDashboardData}
                    disabled={loading}
                  >
                    Refresh Dashboard
                  </Button>
                </View>
              </View>
            </Tabs.Panel>
          </Tabs>
        </View>
      </View>
    </div>
  )
}

export default FacultyConsultationDashboard
