import React, { useState, useEffect } from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { TextInput } from '@instructure/ui-text-input'
import { Select } from '@instructure/ui-select'
import { Checkbox } from '@instructure/ui-checkbox'
import { TextArea } from '@instructure/ui-text-area'
import { FormFieldGroup } from '@instructure/ui-form-field'
import { ScreenReaderContent } from '@instructure/ui-a11y-content'
import { useTimeSlotFormValidation } from '../hooks/useFormValidation'
import type { FacultyTimeSlot, TimeSlotFormData } from '../types'

interface TimeSlotFormProps {
  daysOfWeek: string[]
  initialData?: FacultyTimeSlot | null
  onSubmit: (data: TimeSlotFormData) => Promise<void>
  onCancel: () => void
  loading: boolean
}

const TimeSlotForm: React.FC<TimeSlotFormProps> = ({
  daysOfWeek,
  initialData,
  onSubmit,
  onCancel,
  loading
}) => {
  const initialFormData: TimeSlotFormData = {
    start_time: '',
    end_time: '',
    day_of_week: '',
    is_recurring: true,
    specific_date: '',
    is_available: true,
    notes: ''
  }

  const {
    data: formData,
    errors,
    isSubmitting,
    updateField,
    handleSubmit,
    reset
  } = useTimeSlotFormValidation(initialFormData, onSubmit)

  useEffect(() => {
    if (initialData) {
      const updatedData = {
        start_time: initialData.start_time,
        end_time: initialData.end_time,
        day_of_week: initialData.day_of_week,
        is_recurring: initialData.is_recurring,
        specific_date: initialData.specific_date || '',
        is_available: initialData.is_available,
        notes: initialData.notes || ''
      }
      // Update form data when initialData changes
      Object.keys(updatedData).forEach(key => {
        updateField(key as keyof TimeSlotFormData, updatedData[key as keyof TimeSlotFormData])
      })
    }
  }, [initialData, updateField])

  // Form submission is handled by the validation hook

  const generateTimeOptions = () => {
    const options = []
    for (let hour = 7; hour <= 20; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
        const displayTime = new Date(`2000-01-01T${timeString}:00`).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        })
        options.push({ value: timeString, label: displayTime })
      }
    }
    return options
  }

  const timeOptions = generateTimeOptions()

  return (
    <View as="div" background="secondary" padding="medium" borderRadius="medium">
      <Heading level="h2" margin="0 0 medium 0">
        {initialData ? 'Edit Time Slot' : 'Add New Time Slot'}
      </Heading>

      <form onSubmit={handleSubmit}>
        <FormFieldGroup description="Time Slot Details" layout="stacked">
          <View as="div" display="flex" gap="medium">
            <View as="div" width="50%">
              <Select
                renderLabel="Start Time"
                placeholder="Select start time"
                value={formData.start_time}
                onChange={(e, { value }) => updateField('start_time', value)}
                messages={errors.start_time ? [{ text: errors.start_time, type: 'error' }] : []}
              >
                {timeOptions.map(option => (
                  <Select.Option key={option.value} id={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </View>

            <View as="div" width="50%">
              <Select
                renderLabel="End Time"
                placeholder="Select end time"
                value={formData.end_time}
                onChange={(e, { value }) => updateField('end_time', value)}
                messages={errors.end_time ? [{ text: errors.end_time, type: 'error' }] : []}
              >
                {timeOptions.map(option => (
                  <Select.Option key={option.value} id={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </View>
          </View>

          <Checkbox
            label="Recurring weekly slot"
            checked={formData.is_recurring}
            onChange={(e) => updateField('is_recurring', e.target.checked)}
          />

          {formData.is_recurring ? (
            <Select
              renderLabel="Day of Week"
              placeholder="Select day"
              value={formData.day_of_week}
              onChange={(e, { value }) => updateField('day_of_week', value)}
              messages={errors.day_of_week ? [{ text: errors.day_of_week, type: 'error' }] : []}
            >
              {daysOfWeek.map(day => (
                <Select.Option key={day} id={day} value={day}>
                  {day}
                </Select.Option>
              ))}
            </Select>
          ) : (
            <TextInput
              renderLabel="Specific Date"
              type="date"
              value={formData.specific_date}
              onChange={(e) => updateField('specific_date', e.target.value)}
              messages={errors.specific_date ? [{ text: errors.specific_date, type: 'error' }] : []}
            />
          )}

          <Checkbox
            label="Available for booking"
            checked={formData.is_available}
            onChange={(e) => updateField('is_available', e.target.checked)}
          />

          <TextArea
            label="Notes (optional)"
            placeholder="Add any additional notes about this time slot..."
            value={formData.notes}
            onChange={(e) => updateField('notes', e.target.value)}
            height="4rem"
          />
        </FormFieldGroup>

        <View as="div" margin="large 0 0 0" display="flex" gap="small" justifyItems="end">
          <Button
            type="button"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            color="primary"
            disabled={loading || isSubmitting}
          >
            {loading || isSubmitting ? 'Saving...' : (initialData ? 'Update Time Slot' : 'Create Time Slot')}
          </Button>
        </View>
      </form>
    </View>
  )
}

export default TimeSlotForm
