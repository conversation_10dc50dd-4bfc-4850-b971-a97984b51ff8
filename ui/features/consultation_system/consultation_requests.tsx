import React from 'react'
import <PERSON>act<PERSON><PERSON> from 'react-dom'
import ConsultationRequestsList from './react/ConsultationRequests'

// Initialize the Consultation Requests List
const initializeConsultationRequests = () => {
  const container = document.getElementById('consultation-requests-container')
  if (!container) {
    console.error('Consultation requests container not found')
    return
  }

  const envData = window.ENV?.CONSULTATION_REQUESTS
  if (!envData) {
    console.error('Consultation requests environment data not found')
    return
  }

  ReactDOM.render(
    <ConsultationRequestsList
      currentUserId={envData.current_user_id}
      userRole={envData.user_role}
      initialRequests={envData.requests || []}
      concernTypes={envData.concern_types || []}
      statuses={envData.statuses || []}
    />,
    container
  )
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeConsultationRequests)
} else {
  initializeConsultationRequests()
}

export default initializeConsultationRequests
