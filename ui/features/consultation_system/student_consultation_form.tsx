import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom'
import StudentConsultationForm from './react/StudentConsultationForm'

// Initialize the Student Consultation Form
const initializeStudentConsultationForm = () => {
  const container = document.getElementById('student-consultation-form-container')
  if (!container) {
    console.error('Student consultation form container not found')
    return
  }

  const envData = window.ENV?.STUDENT_CONSULTATION_FORM
  if (!envData) {
    console.error('Student consultation form environment data not found')
    return
  }

  ReactDOM.render(
    <StudentConsultationForm
      currentUserId={envData.current_user_id}
      studentInfo={envData.student_info}
      availableFaculty={envData.available_faculty || []}
      concernTypes={envData.concern_types || []}
    />,
    container
  )
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeStudentConsultationForm)
} else {
  initializeStudentConsultationForm()
}

export default initializeStudentConsultationForm
