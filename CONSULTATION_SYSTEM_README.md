# Consultation Request System for Canvas LMS

A comprehensive consultation request system that allows students to request consultations with faculty members, and faculty to manage their availability and consultation requests.

## Features

### For Students
- **Request Consultations**: Submit consultation requests with available faculty members
- **View Request Status**: Track the status of submitted requests (pending, approved, declined, completed)
- **Manage Appointments**: View upcoming approved consultations
- **Detailed Descriptions**: Provide detailed descriptions of concerns and issues
- **Multiple Concern Types**: Select from various concern categories (Academic, Personal, Teacher-related, etc.)

### For Faculty
- **Time Slot Management**: Create and manage available consultation time slots
- **Request Management**: Review, approve, or decline student consultation requests
- **Dashboard**: Comprehensive dashboard showing pending requests and upcoming consultations
- **Calendar Integration**: Automatic calendar event creation for approved consultations
- **Consultation Summaries**: Create and manage post-consultation summaries
- **Reporting**: View statistics and generate reports on consultation activities

### System Features
- **Role-based Access**: Different interfaces for students and faculty
- **Notification System**: Automated notifications for status changes and reminders
- **Calendar Integration**: Seamless integration with Canvas calendar system
- **Validation & Error Handling**: Comprehensive form validation and error handling
- **Responsive Design**: Mobile-friendly interface
- **Export Capabilities**: CSV export for consultation data

## Installation & Setup

### 1. Database Migration
```bash
rails db:migrate
```

### 2. Install Dependencies
```bash
yarn install
```

### 3. Build Frontend Assets
```bash
yarn build
```

### 4. Initialize Notifications
The system will automatically create notification types on startup. To manually initialize:
```bash
rails runner "ConsultationNotificationService::NOTIFICATION_TYPES.each { |k,v| Notification.find_or_create_by(name: v) }"
```

### 5. Schedule Background Jobs
For consultation reminders, set up a recurring job:
```bash
# Add to your job scheduler (e.g., cron, whenever gem)
ConsultationReminderJob.perform_later
```

## Usage

### Navigation
The consultation system is accessible through the main Canvas navigation under "Consultations".

### Student Workflow
1. Navigate to Consultations → Request Consultation
2. Select a faculty member and available time slot
3. Choose concern type and provide detailed description
4. Submit request and wait for faculty response
5. Receive notification when request is approved/declined
6. Attend scheduled consultation

### Faculty Workflow
1. Navigate to Consultations → Manage Time Slots
2. Create available time slots (recurring or one-time)
3. Review incoming consultation requests in the dashboard
4. Approve or decline requests with optional comments
5. Conduct consultations and mark as complete
6. Create consultation summaries for record-keeping

## API Endpoints

### Faculty Time Slots
- `GET /faculty_time_slots` - List time slots
- `POST /faculty_time_slots` - Create time slot
- `PATCH /faculty_time_slots/:id` - Update time slot
- `DELETE /faculty_time_slots/:id` - Delete time slot
- `GET /faculty_time_slots/available_dates` - Get available dates
- `GET /faculty_time_slots/available_times` - Get available times for date

### Consultation Requests
- `GET /consultation_requests` - List requests
- `POST /consultation_requests` - Create request
- `PATCH /consultation_requests/:id` - Update request
- `POST /consultation_requests/:id/approve` - Approve request
- `POST /consultation_requests/:id/decline` - Decline request
- `POST /consultation_requests/:id/complete` - Complete consultation
- `GET /consultation_requests/faculty_dashboard` - Faculty dashboard data
- `GET /consultation_requests/student_form` - Student form data

### Consultation Summaries
- `GET /consultation_summaries` - List summaries
- `PATCH /consultation_summaries/:id` - Update summary
- `POST /consultation_summaries/:id/add_notes` - Add notes
- `GET /consultation_summaries/dashboard` - Dashboard data
- `GET /consultation_summaries/reports` - Reports data

## Configuration

### Environment Variables
- `CONSULTATION_REMINDER_HOURS` - Hours before consultation to send reminders (default: 24, 2)
- `MAX_PENDING_REQUESTS_PER_STUDENT` - Maximum pending requests per student (default: 3)
- `CONSULTATION_DURATION_MINUTES` - Default consultation duration (default: 30)

### Customization
- **Concern Types**: Modify `CONCERN_TYPES` constant in `app/models/consultation_request.rb`
- **Business Hours**: Adjust validation in models for acceptable consultation hours
- **Notification Templates**: Customize messages in `ConsultationNotificationService`

## Maintenance Tasks

### Calendar Sync
```bash
# Sync all approved consultations to calendar
rake consultation:calendar:sync_all

# Clean up orphaned calendar events
rake consultation:calendar:cleanup

# Update all consultation calendar events
rake consultation:calendar:update_all

# Show statistics
rake consultation:calendar:stats

# Validate calendar integration
rake consultation:calendar:validate

# Run all maintenance tasks
rake consultation:calendar:maintenance
```

### Data Management
```bash
# Generate consultation reports
rails runner "ConsultationSummary.generate_monthly_report(Date.current.year, Date.current.month)"

# Clean up old declined/cancelled requests (older than 6 months)
rails runner "ConsultationRequest.where(status: ['declined', 'cancelled']).where('created_at < ?', 6.months.ago).destroy_all"
```

## Troubleshooting

### Common Issues

1. **Calendar Events Not Creating**
   - Check that CalendarEvent model is available
   - Verify faculty user has calendar access
   - Run `rake consultation:calendar:validate`

2. **Notifications Not Sending**
   - Verify notification types are created
   - Check that Notification model is properly configured
   - Ensure background job processing is running

3. **Time Slot Conflicts**
   - The system automatically checks for calendar conflicts
   - Verify faculty calendar permissions
   - Check for overlapping time slots

4. **Frontend Bundle Issues**
   - Ensure all React components are properly exported
   - Check webpack bundle configuration
   - Verify featureBundles.ts includes consultation entries

### Debugging
Enable detailed logging:
```ruby
# In Rails console
Rails.logger.level = :debug
```

Check system status:
```bash
rake consultation:calendar:stats
```

## File Structure

```
app/
├── controllers/
│   ├── consultation_requests_controller.rb
│   ├── consultation_summaries_controller.rb
│   ├── consultations_controller.rb
│   └── faculty_time_slots_controller.rb
├── models/
│   ├── consultation_request.rb
│   ├── consultation_summary.rb
│   └── faculty_time_slot.rb
├── services/
│   ├── consultation_calendar_service.rb
│   └── consultation_notification_service.rb
├── jobs/
│   └── consultation_reminder_job.rb
└── views/
    ├── consultation_requests/
    ├── consultation_summaries/
    ├── consultations/
    └── faculty_time_slots/

ui/features/consultation_system/
├── react/
│   ├── components/
│   ├── hooks/
│   ├── services/
│   ├── utils/
│   ├── ConsultationNavigation/
│   ├── ConsultationRequests/
│   ├── ConsultationSummaries/
│   ├── FacultyConsultationDashboard/
│   ├── FacultyTimeSlots/
│   └── StudentConsultationForm/
├── consultation_navigation.tsx
├── consultation_requests.tsx
├── consultation_summaries.tsx
├── faculty_consultation_dashboard.tsx
├── faculty_consultations.tsx
├── faculty_time_slots.tsx
├── student_consultation_form.tsx
└── student_consultations.tsx

app/stylesheets/bundles/
└── consultation_system.scss

config/
├── routes.rb (consultation routes)
└── initializers/
    └── consultation_notifications.rb

lib/tasks/
└── consultation_calendar.rake
```

## Contributing

1. Follow existing code patterns and conventions
2. Add tests for new functionality
3. Update documentation for any API changes
4. Ensure responsive design for new UI components
5. Test with both student and faculty user roles

## Support

For technical support or feature requests, please contact the development team or create an issue in the project repository.
