# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ConsultationRequest, type: :model do
  let(:student) { create(:user) }
  let(:faculty) { create(:user) }
  let(:faculty_time_slot) { create(:faculty_time_slot, user: faculty) }
  
  let(:valid_attributes) do
    {
      student: student,
      faculty: faculty,
      faculty_time_slot: faculty_time_slot,
      preferred_datetime: 1.day.from_now,
      description: 'I need help with my coursework and understanding the material better.',
      nature_of_concern: 'Academic'
    }
  end

  describe 'validations' do
    it 'is valid with valid attributes' do
      request = ConsultationRequest.new(valid_attributes)
      expect(request).to be_valid
    end

    it 'requires a student' do
      request = ConsultationRequest.new(valid_attributes.except(:student))
      expect(request).not_to be_valid
      expect(request.errors[:student]).to include("can't be blank")
    end

    it 'requires a faculty' do
      request = ConsultationRequest.new(valid_attributes.except(:faculty))
      expect(request).not_to be_valid
      expect(request.errors[:faculty]).to include("can't be blank")
    end

    it 'requires a preferred_datetime' do
      request = ConsultationRequest.new(valid_attributes.except(:preferred_datetime))
      expect(request).not_to be_valid
      expect(request.errors[:preferred_datetime]).to include("can't be blank")
    end

    it 'requires a description' do
      request = ConsultationRequest.new(valid_attributes.except(:description))
      expect(request).not_to be_valid
      expect(request.errors[:description]).to include("can't be blank")
    end

    it 'requires a nature_of_concern' do
      request = ConsultationRequest.new(valid_attributes.except(:nature_of_concern))
      expect(request).not_to be_valid
      expect(request.errors[:nature_of_concern]).to include("can't be blank")
    end

    it 'validates description length' do
      request = ConsultationRequest.new(valid_attributes.merge(description: 'Too short'))
      expect(request).not_to be_valid
      expect(request.errors[:description]).to include('must be at least 20 characters long')
    end

    it 'validates description is meaningful' do
      request = ConsultationRequest.new(valid_attributes.merge(description: 'test'))
      expect(request).not_to be_valid
      expect(request.errors[:description]).to include('must provide a meaningful description of your concern')
    end

    it 'validates preferred_datetime is in the future' do
      request = ConsultationRequest.new(valid_attributes.merge(preferred_datetime: 1.hour.ago))
      expect(request).not_to be_valid
      expect(request.errors[:preferred_datetime]).to include('must be in the future')
    end

    it 'validates preferred_datetime is during business hours' do
      request = ConsultationRequest.new(valid_attributes.merge(preferred_datetime: Time.zone.now.beginning_of_day + 5.hours))
      expect(request).not_to be_valid
      expect(request.errors[:preferred_datetime]).to include('must be during business hours (7 AM - 8 PM)')
    end

    it 'validates preferred_datetime is on weekdays' do
      next_sunday = Time.zone.now.next_occurring(:sunday) + 10.hours
      request = ConsultationRequest.new(valid_attributes.merge(preferred_datetime: next_sunday))
      expect(request).not_to be_valid
      expect(request.errors[:preferred_datetime]).to include('must be during weekdays (Monday-Friday)')
    end

    it 'prevents duplicate pending requests' do
      ConsultationRequest.create!(valid_attributes)
      duplicate_request = ConsultationRequest.new(valid_attributes)
      expect(duplicate_request).not_to be_valid
      expect(duplicate_request.errors[:base]).to include('You already have a pending consultation request with this faculty member')
    end

    it 'limits pending requests per student' do
      3.times do |i|
        other_faculty = create(:user)
        create(:consultation_request, 
               student: student, 
               faculty: other_faculty, 
               status: 'pending')
      end
      
      request = ConsultationRequest.new(valid_attributes)
      expect(request).not_to be_valid
      expect(request.errors[:base]).to include('You cannot have more than 3 pending consultation requests at a time')
    end
  end

  describe 'status transitions' do
    let(:request) { create(:consultation_request, valid_attributes) }

    it 'allows transition from pending to approved' do
      request.update(status: 'approved')
      expect(request).to be_valid
      expect(request.status).to eq('approved')
    end

    it 'allows transition from pending to declined' do
      request.update(status: 'declined')
      expect(request).to be_valid
      expect(request.status).to eq('declined')
    end

    it 'allows transition from approved to completed' do
      request.update(status: 'approved')
      request.update(status: 'completed')
      expect(request).to be_valid
      expect(request.status).to eq('completed')
    end

    it 'prevents invalid status transitions' do
      request.update(status: 'completed')
      request.update(status: 'pending')
      expect(request).not_to be_valid
      expect(request.errors[:status]).to include('cannot transition from completed to pending')
    end
  end

  describe 'scopes' do
    let!(:pending_request) { create(:consultation_request, status: 'pending') }
    let!(:approved_request) { create(:consultation_request, status: 'approved') }
    let!(:completed_request) { create(:consultation_request, status: 'completed') }

    it 'filters by status' do
      expect(ConsultationRequest.pending).to include(pending_request)
      expect(ConsultationRequest.pending).not_to include(approved_request)
    end

    it 'filters by concern type' do
      academic_request = create(:consultation_request, nature_of_concern: 'Academic')
      personal_request = create(:consultation_request, nature_of_concern: 'Personal')
      
      expect(ConsultationRequest.by_concern_type('Academic')).to include(academic_request)
      expect(ConsultationRequest.by_concern_type('Academic')).not_to include(personal_request)
    end

    it 'filters recent requests' do
      old_request = create(:consultation_request, created_at: 2.months.ago)
      recent_request = create(:consultation_request, created_at: 1.day.ago)
      
      expect(ConsultationRequest.recent).to include(recent_request)
      expect(ConsultationRequest.recent).not_to include(old_request)
    end
  end

  describe 'callbacks' do
    it 'sets student info on create' do
      student.update(name: 'John Doe')
      request = ConsultationRequest.create!(valid_attributes)
      expect(request.student_name).to eq('John Doe')
      expect(request.student_id).to eq(student.id.to_s)
    end

    it 'sends notification on create' do
      expect(ConsultationNotificationService).to receive(:send_request_submitted_notification)
      ConsultationRequest.create!(valid_attributes)
    end

    it 'creates consultation summary when completed' do
      request = create(:consultation_request, status: 'approved')
      expect {
        request.update!(status: 'completed')
      }.to change(ConsultationSummary, :count).by(1)
    end

    it 'syncs calendar event on status change' do
      request = create(:consultation_request, status: 'pending')
      expect(ConsultationCalendarService).to receive(:create_calendar_event_for_consultation)
      request.update!(status: 'approved')
    end
  end

  describe 'helper methods' do
    let(:request) { create(:consultation_request, valid_attributes) }

    it 'formats preferred datetime' do
      expect(request.formatted_preferred_datetime).to be_a(String)
      expect(request.formatted_preferred_datetime).to include(request.preferred_datetime.strftime('%A'))
    end

    it 'returns concern type display' do
      expect(request.concern_type_display).to eq('Academic')
    end

    it 'returns status display' do
      expect(request.status_display).to eq('Pending')
    end

    it 'checks if can be approved' do
      expect(request.can_be_approved?).to be true
      request.update(status: 'completed')
      expect(request.can_be_approved?).to be false
    end

    it 'checks if can be declined' do
      expect(request.can_be_declined?).to be true
      request.update(status: 'completed')
      expect(request.can_be_declined?).to be false
    end

    it 'checks if can be completed' do
      request.update(status: 'approved')
      expect(request.can_be_completed?).to be true
      request.update(status: 'pending')
      expect(request.can_be_completed?).to be false
    end
  end
end
