# frozen_string_literal: true

class ConsultationSummariesController < ApplicationController
  before_action :require_user
  before_action :require_faculty_access
  before_action :set_consultation_summary, only: [:show, :update]

  # GET /consultation_summaries
  def index
    @summaries = current_user.faculty_consultation_summaries.includes(:student, :consultation_request)
                            .order(consultation_date: :desc)

    # Apply filters
    @summaries = @summaries.by_concern_type(params[:concern_type]) if params[:concern_type].present?
    @summaries = @summaries.search_by_student(params[:student_search]) if params[:student_search].present?
    @summaries = @summaries.search_by_content(params[:content_search]) if params[:content_search].present?
    
    if params[:start_date].present? && params[:end_date].present?
      start_date = Date.parse(params[:start_date]) rescue nil
      end_date = Date.parse(params[:end_date]) rescue nil
      @summaries = @summaries.in_date_range(start_date, end_date) if start_date && end_date
    end

    # Pagination
    page = params[:page]&.to_i || 1
    per_page = params[:per_page]&.to_i || 20
    per_page = [per_page, 100].min # Max 100 per page

    @summaries = @summaries.limit(per_page).offset((page - 1) * per_page)

    respond_to do |format|
      format.json { render json: consultation_summaries_json(@summaries) }
      format.html { render_consultation_summaries_page }
      format.csv { send_csv_export }
    end
  end

  # GET /consultation_summaries/:id
  def show
    respond_to do |format|
      format.json { render json: consultation_summary_json(@consultation_summary) }
    end
  end

  # PATCH/PUT /consultation_summaries/:id
  def update
    if @consultation_summary.update(consultation_summary_params)
      respond_to do |format|
        format.json { render json: consultation_summary_json(@consultation_summary) }
      end
    else
      respond_to do |format|
        format.json { render json: { errors: @consultation_summary.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # GET /consultation_summaries/dashboard
  def dashboard
    @statistics = current_user.consultation_statistics
    @concern_type_breakdown = ConsultationSummary.grouped_by_concern_type_for_faculty(current_user)
    @recent_summaries = current_user.faculty_consultation_summaries.recent.limit(10)
    @summaries_requiring_follow_up = current_user.faculty_consultation_summaries.requiring_follow_up.recent.limit(5)

    respond_to do |format|
      format.json { 
        render json: {
          statistics: @statistics,
          concern_type_breakdown: @concern_type_breakdown,
          recent_summaries: consultation_summaries_json(@recent_summaries)[:summaries],
          follow_up_required: consultation_summaries_json(@summaries_requiring_follow_up)[:summaries]
        }
      }
      format.html { render_dashboard_page }
    end
  end

  # GET /consultation_summaries/reports
  def reports
    @year = params[:year]&.to_i || Date.current.year
    @monthly_data = ConsultationSummary.monthly_summary_for_faculty(current_user, @year)
    @concern_type_stats = ConsultationSummary.grouped_by_concern_type_for_faculty(current_user)
    @total_consultations = current_user.faculty_consultation_summaries.count

    respond_to do |format|
      format.json { 
        render json: {
          year: @year,
          monthly_data: @monthly_data,
          concern_type_stats: @concern_type_stats,
          total_consultations: @total_consultations
        }
      }
      format.html { render_reports_page }
    end
  end

  # POST /consultation_summaries/:id/add_notes
  def add_notes
    @consultation_summary = current_user.faculty_consultation_summaries.find(params[:id])
    notes = params[:notes]

    if notes.present?
      @consultation_summary.add_faculty_notes!(notes)
      respond_to do |format|
        format.json { render json: consultation_summary_json(@consultation_summary) }
      end
    else
      respond_to do |format|
        format.json { render json: { errors: ['Notes cannot be blank'] }, status: :unprocessable_entity }
      end
    end
  end

  private

  def require_faculty_access
    unless current_user.teacher_enrollments.active.exists?
      respond_to do |format|
        format.json { render json: { error: 'Access denied. Faculty access required.' }, status: :forbidden }
        format.html {
          flash[:error] = 'Access denied. Faculty access required.'
          redirect_to root_path
        }
      end
    end
  end

  def set_consultation_summary
    @consultation_summary = current_user.faculty_consultation_summaries.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    respond_to do |format|
      format.json { render json: { error: 'Consultation summary not found' }, status: :not_found }
      format.html { 
        flash[:error] = 'Consultation summary not found'
        redirect_to consultation_summaries_path 
      }
    end
  end

  def consultation_summary_params
    params.require(:consultation_summary).permit(
      :faculty_notes, :outcome_summary, :referral_made, :follow_up_required
    )
  end

  def consultation_summary_json(summary)
    {
      id: summary.id,
      student_name: summary.student_name,
      student_id: summary.student_id,
      consultation_date: summary.consultation_date.iso8601,
      formatted_date: summary.formatted_consultation_date,
      concern_type: summary.concern_type,
      concern_type_display: summary.concern_type_display,
      description: summary.description,
      faculty_notes: summary.faculty_notes,
      outcome_summary: summary.outcome_summary,
      referral_made: summary.referral_made,
      follow_up_required: summary.follow_up_required,
      has_referral: summary.has_referral?,
      requires_follow_up: summary.requires_follow_up?,
      duration_display: summary.duration_display,
      created_at: summary.created_at.iso8601,
      updated_at: summary.updated_at.iso8601
    }
  end

  def consultation_summaries_json(summaries)
    {
      summaries: summaries.map { |summary| consultation_summary_json(summary) },
      total_count: summaries.respond_to?(:count) ? summaries.count : summaries.size
    }
  end

  def send_csv_export
    summaries = current_user.faculty_consultation_summaries.includes(:student, :consultation_request)
    
    # Apply same filters as index
    summaries = summaries.by_concern_type(params[:concern_type]) if params[:concern_type].present?
    summaries = summaries.search_by_student(params[:student_search]) if params[:student_search].present?
    summaries = summaries.search_by_content(params[:content_search]) if params[:content_search].present?
    
    if params[:start_date].present? && params[:end_date].present?
      start_date = Date.parse(params[:start_date]) rescue nil
      end_date = Date.parse(params[:end_date]) rescue nil
      summaries = summaries.in_date_range(start_date, end_date) if start_date && end_date
    end

    csv_data = ConsultationSummary.to_csv(summaries)
    filename = "consultation_summaries_#{Date.current.strftime('%Y%m%d')}.csv"
    
    send_data csv_data, 
              filename: filename,
              type: 'text/csv',
              disposition: 'attachment'
  end

  def render_consultation_summaries_page
    @page_title = 'Consultation Summaries'
    js_env({
      CONSULTATION_SUMMARIES: {
        current_user_id: current_user.id,
        summaries: consultation_summaries_json(@summaries)[:summaries],
        concern_types: ConsultationSummary::CONCERN_TYPES,
        filters: {
          concern_type: params[:concern_type],
          student_search: params[:student_search],
          content_search: params[:content_search],
          start_date: params[:start_date],
          end_date: params[:end_date]
        }
      }
    })
    
    js_bundle :consultation_summaries
    css_bundle :consultation_system
    render html: "".html_safe, layout: true
  end

  def render_dashboard_page
    @page_title = 'Consultation Summary Dashboard'
    js_env({
      CONSULTATION_DASHBOARD: {
        current_user_id: current_user.id,
        statistics: @statistics,
        concern_type_breakdown: @concern_type_breakdown,
        recent_summaries: consultation_summaries_json(@recent_summaries)[:summaries],
        follow_up_required: consultation_summaries_json(@summaries_requiring_follow_up)[:summaries]
      }
    })
    
    js_bundle :consultation_summary_dashboard
    css_bundle :consultation_system
    render html: "".html_safe, layout: true
  end

  def render_reports_page
    @page_title = 'Consultation Reports'
    js_env({
      CONSULTATION_REPORTS: {
        current_user_id: current_user.id,
        year: @year,
        monthly_data: @monthly_data,
        concern_type_stats: @concern_type_stats,
        total_consultations: @total_consultations
      }
    })
    
    js_bundle :consultation_reports
    css_bundle :consultation_system
    render html: "".html_safe, layout: true
  end
end
