# frozen_string_literal: true

class ConsultationNotificationService
  # Notification types for consultation system
  NOTIFICATION_TYPES = {
    request_submitted: 'Consultation Request Submitted',
    request_approved: 'Consultation Request Approved', 
    request_declined: 'Consultation Request Declined',
    consultation_reminder: 'Consultation Reminder',
    consultation_completed: 'Consultation Completed'
  }.freeze

  def self.send_request_submitted_notification(consultation_request)
    # Notify faculty that a new consultation request has been submitted
    notification = find_or_create_notification(:request_submitted)
    
    notification.create_message(
      consultation_request,
      [consultation_request.faculty],
      {
        subject: "New Consultation Request from #{consultation_request.student_name}",
        body: build_request_submitted_body(consultation_request),
        data: {
          consultation_request_id: consultation_request.id,
          student_name: consultation_request.student_name,
          concern_type: consultation_request.nature_of_concern
        }
      }
    )
  end

  def self.send_request_approved_notification(consultation_request)
    # Notify student that their consultation request has been approved
    notification = find_or_create_notification(:request_approved)
    
    notification.create_message(
      consultation_request,
      [consultation_request.student],
      {
        subject: "Your Consultation Request has been Approved",
        body: build_request_approved_body(consultation_request),
        data: {
          consultation_request_id: consultation_request.id,
          faculty_name: consultation_request.faculty.name,
          consultation_datetime: consultation_request.preferred_datetime.iso8601
        }
      }
    )
  end

  def self.send_request_declined_notification(consultation_request)
    # Notify student that their consultation request has been declined
    notification = find_or_create_notification(:request_declined)
    
    notification.create_message(
      consultation_request,
      [consultation_request.student],
      {
        subject: "Your Consultation Request has been Declined",
        body: build_request_declined_body(consultation_request),
        data: {
          consultation_request_id: consultation_request.id,
          faculty_name: consultation_request.faculty.name,
          decline_reason: consultation_request.faculty_comment
        }
      }
    )
  end

  def self.send_consultation_reminder(consultation_request, hours_before = 24)
    # Send reminder notification before consultation
    notification = find_or_create_notification(:consultation_reminder)
    
    # Send to both student and faculty
    recipients = [consultation_request.student, consultation_request.faculty]
    
    notification.create_message(
      consultation_request,
      recipients,
      {
        subject: "Consultation Reminder - #{hours_before} hours",
        body: build_consultation_reminder_body(consultation_request, hours_before),
        data: {
          consultation_request_id: consultation_request.id,
          consultation_datetime: consultation_request.preferred_datetime.iso8601,
          hours_before: hours_before
        }
      }
    )
  end

  def self.send_consultation_completed_notification(consultation_request)
    # Notify student that consultation has been completed
    notification = find_or_create_notification(:consultation_completed)
    
    notification.create_message(
      consultation_request,
      [consultation_request.student],
      {
        subject: "Consultation Completed",
        body: build_consultation_completed_body(consultation_request),
        data: {
          consultation_request_id: consultation_request.id,
          faculty_name: consultation_request.faculty.name,
          completion_notes: consultation_request.faculty_comment
        }
      }
    )
  end

  # Schedule reminder notifications for upcoming consultations
  def self.schedule_consultation_reminders
    # Find consultations that need 24-hour reminders
    consultations_24h = ConsultationRequest.approved
                                          .where(preferred_datetime: 24.hours.from_now..25.hours.from_now)
                                          .includes(:student, :faculty)

    consultations_24h.find_each do |consultation|
      send_consultation_reminder(consultation, 24)
    end

    # Find consultations that need 2-hour reminders  
    consultations_2h = ConsultationRequest.approved
                                         .where(preferred_datetime: 2.hours.from_now..3.hours.from_now)
                                         .includes(:student, :faculty)

    consultations_2h.find_each do |consultation|
      send_consultation_reminder(consultation, 2)
    end
  end

  private

  def self.find_or_create_notification(type)
    notification_name = NOTIFICATION_TYPES[type]
    category = determine_category(type)
    
    Notification.find_by(name: notification_name) ||
      Notification.create!(
        name: notification_name,
        category: category,
        subject: notification_name,
        main_link: '/consultations'
      )
  end

  def self.determine_category(type)
    case type
    when :request_submitted, :request_approved, :request_declined
      'Consultation'
    when :consultation_reminder
      'Calendar'
    when :consultation_completed
      'Consultation'
    else
      'Other'
    end
  end

  def self.build_request_submitted_body(consultation_request)
    <<~BODY
      A new consultation request has been submitted by #{consultation_request.student_name}.

      Student: #{consultation_request.student_name} (#{consultation_request.student_id})
      Requested Time: #{consultation_request.formatted_preferred_datetime}
      Concern Type: #{consultation_request.concern_type_display}

      Description:
      #{consultation_request.description}

      Please review and respond to this request in your faculty consultation dashboard.

      View Request: #{Rails.application.routes.url_helpers.consultation_requests_url}/#{consultation_request.id}
    BODY
  end

  def self.build_request_approved_body(consultation_request)
    <<~BODY
      Great news! Your consultation request has been approved.

      Faculty: #{consultation_request.faculty.name}
      Scheduled Time: #{consultation_request.formatted_preferred_datetime}
      Concern Type: #{consultation_request.concern_type_display}

      #{consultation_request.faculty_comment if consultation_request.faculty_comment.present?}

      Please make sure to attend your scheduled consultation. If you need to cancel or reschedule, please contact your faculty member as soon as possible.

      View Details: #{Rails.application.routes.url_helpers.consultation_requests_url}
    BODY
  end

  def self.build_request_declined_body(consultation_request)
    <<~BODY
      Your consultation request has been declined.

      Faculty: #{consultation_request.faculty.name}
      Requested Time: #{consultation_request.formatted_preferred_datetime}
      Concern Type: #{consultation_request.concern_type_display}

      #{consultation_request.faculty_comment if consultation_request.faculty_comment.present?}

      You may submit a new consultation request with different time preferences or contact the faculty member directly.

      Submit New Request: #{Rails.application.routes.url_helpers.consultation_requests_url}/student_form
    BODY
  end

  def self.build_consultation_reminder_body(consultation_request, hours_before)
    <<~BODY
      This is a reminder about your upcoming consultation.

      Student: #{consultation_request.student_name}
      Faculty: #{consultation_request.faculty.name}
      Scheduled Time: #{consultation_request.formatted_preferred_datetime}
      Concern Type: #{consultation_request.concern_type_display}

      The consultation is scheduled to begin in #{hours_before} hour#{'s' if hours_before != 1}.

      Please make sure you are prepared and available at the scheduled time.

      View Details: #{Rails.application.routes.url_helpers.consultation_requests_url}
    BODY
  end

  def self.build_consultation_completed_body(consultation_request)
    <<~BODY
      Your consultation has been completed.

      Faculty: #{consultation_request.faculty.name}
      Consultation Date: #{consultation_request.formatted_preferred_datetime}
      Concern Type: #{consultation_request.concern_type_display}

      #{consultation_request.faculty_comment if consultation_request.faculty_comment.present?}

      Thank you for using the consultation system. If you need additional support, please don't hesitate to submit another consultation request.

      Submit New Request: #{Rails.application.routes.url_helpers.consultation_requests_url}/student_form
    BODY
  end
end
